
> exchange-mall@0.0.1 dev
> vite


  VITE v4.5.9  ready in 207 ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
DEPRECATION WARNING [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api

[1m[33m[@vue/compiler-sfc][0m[33m `defineProps` is a compiler macro and no longer needs to be imported.[0m

[1m[33m[@vue/compiler-sfc][0m[33m `defineEmits` is a compiler macro and no longer needs to be imported.[0m

files in the public directory are served at the root path.
Instead of /public/images/payment/pay.png, use /images/payment/pay.png.
files in the public directory are served at the root path.
Instead of /public/images/payment/pay.png, use /images/payment/pay.png.
files in the public directory are served at the root path.
Instead of /public/images/payment/pay.png, use /images/payment/pay.png.
18:18:44 [vite] hmr update /src/views/admin/help/SystemIntro.vue
18:19:01 [vite] hmr update /src/views/admin/help/SystemIntro.vue
18:21:32 [vite] hmr update /src/views/admin/HelpCenter.vue
18:21:45 [vite] hmr update /src/views/admin/HelpCenter.vue
18:21:58 [vite] hmr update /src/views/admin/HelpCenter.vue
18:22:16 [vite] hmr update /src/views/admin/help/FAQ.vue
18:22:31 [vite] hmr update /src/views/admin/help/FAQ.vue
18:22:52 [vite] hmr update /src/views/admin/help/FAQ.vue
18:23:20 [vite] hmr update /src/views/admin/help/SystemUpdates.vue
18:24:20 [vite] hmr update /src/views/admin/HelpCenter.vue
18:24:31 [vite] hmr update /src/views/admin/HelpCenter.vue
18:24:42 [vite] hmr update /src/views/admin/HelpCenter.vue
