import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import { fileURLToPath } from 'url'

// 获取当前文件的目录
const __dirname = path.dirname(fileURLToPath(import.meta.url))

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },
  server: {
    proxy: {
      // 配置代理
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path
      }
    }
  },

  // 构建优化配置
  build: {
    // 启用源码映射（生产环境可关闭）
    sourcemap: false,

    // 构建目标
    target: 'es2015',

    // 代码分割配置
    rollupOptions: {
      output: {
        // 手动分割代码块
        manualChunks: {
          // Vue核心库
          'vue-vendor': ['vue', 'vue-router', 'pinia'],

          // UI组件库
          'ui-vendor': ['element-plus', '@element-plus/icons-vue'],

          // 工具库
          'utils-vendor': ['axios', 'date-fns', 'uuid'],

          // 图表库
          'charts-vendor': ['echarts', 'vue-echarts'],

          // 编辑器
          'editor-vendor': ['@wangeditor/editor', '@wangeditor/editor-for-vue']
        },

        // 文件命名规则
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.')
          const ext = info[info.length - 1]
          if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)) {
            return `media/[name]-[hash].${ext}`
          }
          if (/\.(png|jpe?g|gif|svg)(\?.*)?$/i.test(assetInfo.name)) {
            return `images/[name]-[hash].${ext}`
          }
          if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
            return `fonts/[name]-[hash].${ext}`
          }
          return `assets/[name]-[hash].${ext}`
        }
      }
    },

    // 压缩配置
    minify: 'terser',
    terserOptions: {
      compress: {
        // 移除console.log
        drop_console: true,
        // 移除debugger
        drop_debugger: true,
        // 移除无用代码
        dead_code: true
      }
    },

    // 资源内联阈值
    assetsInlineLimit: 4096,

    // 启用CSS代码分割
    cssCodeSplit: true,

    // 构建报告
    reportCompressedSize: false
  },

  // 优化依赖预构建
  optimizeDeps: {
    include: [
      'vue',
      'vue-router',
      'pinia',
      'element-plus',
      '@element-plus/icons-vue',
      'axios',
      'date-fns',
      'echarts',
      'vue-echarts'
    ],
    exclude: [
      // 排除一些不需要预构建的包
    ]
  },

  // CSS配置
  css: {
    // CSS预处理器配置
    preprocessorOptions: {
      scss: {
        additionalData: `@use "@/styles/variables.scss" as *;`
      }
    },

    // PostCSS配置
    postcss: {
      plugins: [
        // 可以添加autoprefixer等插件
      ]
    }
  },

  // 预览服务器配置
  preview: {
    port: 4173,
    strictPort: true
  }
})