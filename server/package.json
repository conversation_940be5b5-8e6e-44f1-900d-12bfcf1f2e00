{"name": "exchange-mall-server", "version": "1.0.0", "description": "Backend for Exchange Mall application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node utils/dbInit.js", "migrate": "npx sequelize-cli db:migrate", "migrate:product-notification": "sequelize-cli db:migrate --name=add-product-notification-type.js", "migrate:undo": "npx sequelize-cli db:migrate:undo", "generate-order-numbers": "node scripts/generate_order_numbers.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"archiver": "^7.0.1", "axios": "^1.8.4", "bcryptjs": "^2.4.3", "compression": "^1.8.0", "cors": "^2.8.5", "csv-parser": "^3.2.0", "csv-writer": "^1.6.0", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "express-fileupload": "^1.5.1", "express-validator": "^7.0.1", "fast-csv": "^5.0.2", "handlebars": "^4.7.8", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.1", "node-cron": "^4.1.0", "pdfkit": "^0.17.1", "pm2": "^6.0.5", "sequelize": "^6.33.0", "sqlite3": "^5.1.7"}, "devDependencies": {"nodemon": "^3.0.1", "sequelize-cli": "^6.6.2"}}